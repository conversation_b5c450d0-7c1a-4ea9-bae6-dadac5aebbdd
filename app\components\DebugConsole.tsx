'use client';

import { useEffect } from 'react';

export default function DebugConsole() {
  useEffect(() => {
    // Override console methods to catch errors
    const originalError = console.error;
    const originalWarn = console.warn;
    const originalLog = console.log;

    console.error = (...args) => {
      originalError('🔴 ERROR:', ...args);
    };

    console.warn = (...args) => {
      originalWarn('🟡 WARNING:', ...args);
    };

    console.log = (...args) => {
      originalLog('🔵 LOG:', ...args);
    };

    // Check for video files
    const checkVideoFiles = async () => {
      const videos = ['/video/orizzontale.mp4', '/video/verticale.mp4'];
      
      for (const video of videos) {
        try {
          const response = await fetch(video, { method: 'HEAD' });
          if (response.ok) {
            console.log(`✅ Video file found: ${video}`);
          } else {
            console.error(`❌ Video file not found: ${video} (Status: ${response.status})`);
          }
        } catch (error) {
          console.error(`❌ Error checking video file ${video}:`, error);
        }
      }
    };

    // Check for image files
    const checkImageFiles = async () => {
      const images = [
        '/images/logo/favicon.png',
        '/images/logo/logo_giallo_topbar.png',
        '/images/carousel/DSC02787.webp',
        '/images/carousel/DSC02814.webp',
        '/images/carousel/DSC02842.webp',
        '/images/carousel/DSC02861.webp',
        '/images/carousel/DSC02891.webp',
        '/images/carousel/DSC02915.webp',
        '/images/carousel/DSC02955.webp'
      ];
      
      for (const image of images) {
        try {
          const response = await fetch(image, { method: 'HEAD' });
          if (response.ok) {
            console.log(`✅ Image file found: ${image}`);
          } else {
            console.error(`❌ Image file not found: ${image} (Status: ${response.status})`);
          }
        } catch (error) {
          console.error(`❌ Error checking image file ${image}:`, error);
        }
      }
    };

    // Check for PDF file
    const checkPdfFile = async () => {
      try {
        const response = await fetch('/pdf/menu_romoletto.pdf', { method: 'HEAD' });
        if (response.ok) {
          console.log('✅ PDF file found: /pdf/menu_romoletto.pdf');
        } else {
          console.error(`❌ PDF file not found: /pdf/menu_romoletto.pdf (Status: ${response.status})`);
        }
      } catch (error) {
        console.error('❌ Error checking PDF file:', error);
      }
    };

    // Run checks
    console.log('🔍 Starting file checks...');
    checkVideoFiles();
    checkImageFiles();
    checkPdfFile();

    // Check environment variables
    console.log('🔍 Environment check:');
    console.log('NODE_ENV:', process.env.NODE_ENV);
    console.log('GA_MEASUREMENT_ID:', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID ? '✅ Set' : '❌ Not set');

    // Listen for unhandled errors
    const handleError = (event: ErrorEvent) => {
      console.error('🔴 Unhandled error:', event.error);
    };

    const handleRejection = (event: PromiseRejectionEvent) => {
      console.error('🔴 Unhandled promise rejection:', event.reason);
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleRejection);

    return () => {
      // Restore original console methods
      console.error = originalError;
      console.warn = originalWarn;
      console.log = originalLog;
      
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleRejection);
    };
  }, []);

  return null; // This component doesn't render anything
}
