{"name": "romoletto", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@next/third-parties": "^15.5.2", "@types/nodemailer": "^7.0.1", "framer-motion": "^12.23.12", "lucide-react": "^0.542.0", "next": "15.5.2", "nodemailer": "^7.0.6", "react": "19.1.0", "react-cookie-consent": "^9.0.0", "react-device-detect": "^2.2.3", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "typescript": "^5"}}