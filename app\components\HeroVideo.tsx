'use client';

import { useState, useEffect, useRef } from 'react';

export default function HeroVideo() {
  const [videoSrc, setVideoSrc] = useState('/video/orizzontale.mp4');
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    // Determine video source based on device and orientation
    const updateVideoSource = () => {
      if (typeof window !== 'undefined') {
        const isPortrait = window.innerHeight > window.innerWidth;
        const isMobileDevice = window.innerWidth <= 768;

        if (isMobileDevice && isPortrait) {
          setVideoSrc('/video/verticale.mp4');
        } else {
          setVideoSrc('/video/orizzontale.mp4');
        }
      }
    };

    // Set initial video source
    updateVideoSource();

    // Listen for orientation changes
    const handleResize = () => {
      updateVideoSource();
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  }, []);

  const handleVideoLoad = () => {
    console.log('Video loaded successfully');
    setIsLoading(false);
    setHasError(false);
  };

  const handleCanPlay = () => {
    console.log('Video can play');
    if (videoRef.current) {
      videoRef.current.play().catch((error) => {
        console.error('Video autoplay failed:', error);
      });
    }
  };

  const handleVideoError = (e: any) => {
    console.error('Video failed to load:', e);
    setIsLoading(false);
    setHasError(true);
  };

  return (
    <section className="relative h-screen w-full overflow-hidden">
      {/* Loading overlay */}
      {isLoading && !hasError && (
        <div className="absolute inset-0 bg-dark-gray flex items-center justify-center z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-yellow mx-auto mb-4"></div>
            <p className="text-primary-yellow font-body">Caricamento video...</p>
            <p className="text-white/60 text-sm mt-2">Sorgente: {videoSrc}</p>
          </div>
        </div>
      )}

      {/* Error overlay */}
      {hasError && (
        <div className="absolute inset-0 bg-dark-gray flex items-center justify-center z-10">
          <div className="text-center">
            <p className="text-primary-red font-body mb-4">Errore nel caricamento del video</p>
            <p className="text-white/60 text-sm">Sorgente: {videoSrc}</p>
            <button
              onClick={() => {
                setHasError(false);
                setIsLoading(true);
                if (videoRef.current) {
                  videoRef.current.load();
                }
              }}
              className="btn-romoletto mt-4"
            >
              Riprova
            </button>
          </div>
        </div>
      )}

      {/* Video Background */}
      <video
        ref={videoRef}
        key={videoSrc} // Force re-render when source changes
        className="absolute top-0 left-0 w-full h-full object-cover"
        autoPlay
        muted
        loop
        playsInline
        preload="auto"
        onLoadedData={handleVideoLoad}
        onCanPlay={handleCanPlay}
        onError={handleVideoError}
        poster="/images/carousel/DSC02787.webp" // Fallback poster image
      >
        <source src={videoSrc} type="video/mp4" />
        <p className="text-white text-center p-8">
          Il tuo browser non supporta i video HTML5.
          <a href={videoSrc} className="text-primary-yellow underline ml-2">
            Scarica il video
          </a>
        </p>
      </video>

      {/* Overlay for better text readability if needed */}
      <div className="absolute inset-0 bg-black bg-opacity-20"></div>

      {/* Optional scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="animate-bounce">
          <svg
            className="w-6 h-6 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 14l-7 7m0 0l-7-7m7 7V3"
            />
          </svg>
        </div>
      </div>
    </section>
  );
}
