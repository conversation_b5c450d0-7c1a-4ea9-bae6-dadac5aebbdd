@import "tailwindcss";

:root {
  --background: #F5F5F5;
  --foreground: #222222;
  --primary-red: #E63422;
  --primary-yellow: #F7C629;
  --dark-red: #B1220E;
  --off-white: #F5F5F5;
  --dark-gray: #222222;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary-red: var(--primary-red);
  --color-primary-yellow: var(--primary-yellow);
  --color-dark-red: var(--dark-red);
  --color-off-white: var(--off-white);
  --color-dark-gray: var(--dark-gray);
  --font-heading: var(--font-heading);
  --font-body: var(--font-body);
  --font-button: var(--font-button);
  --font-sans: var(--font-body);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-body);
  line-height: 1.6;
}

/* Custom button styles */
.btn-romoletto {
  background-color: var(--primary-red);
  color: var(--primary-yellow);
  border: none;
  padding: 12px 24px;
  font-family: var(--font-button);
  font-weight: 600;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-romoletto:hover {
  background-color: var(--primary-yellow);
  color: var(--primary-red);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Additional responsive utilities */
@media (max-width: 768px) {
  .btn-romoletto {
    padding: 10px 20px;
    font-size: 14px;
  }
}

/* Focus states for accessibility */
.btn-romoletto:focus,
button:focus,
a:focus {
  outline: 2px solid var(--primary-yellow);
  outline-offset: 2px;
}

/* Loading animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--off-white);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-red);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--dark-red);
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
