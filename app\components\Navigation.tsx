'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Menu, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

export default function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <>
      <nav
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          isScrolled
            ? 'bg-primary-yellow shadow-lg'
            : 'bg-transparent'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16 lg:h-20">
            {/* Logo */}
            <Link href="/" className="flex-shrink-0">
              <Image
                src="/images/logo/logo_giallo_topbar.png"
                alt="Romoletto Logo"
                width={120}
                height={40}
                className="h-8 lg:h-10 w-auto"
                priority
              />
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex items-center space-x-8">
              <Link
                href="#storia"
                className={`font-body font-medium transition-colors duration-200 ${
                  isScrolled
                    ? 'text-primary-red hover:text-dark-red'
                    : 'text-white hover:text-primary-yellow'
                }`}
              >
                La Nostra Storia
              </Link>
              <Link
                href="#lavora"
                className={`font-body font-medium transition-colors duration-200 ${
                  isScrolled
                    ? 'text-primary-red hover:text-dark-red'
                    : 'text-white hover:text-primary-yellow'
                }`}
              >
                Lavora con Noi
              </Link>
              <Link
                href="#prenotazione"
                className="btn-romoletto"
              >
                Prenotazione
              </Link>
            </div>

            {/* Mobile menu button */}
            <button
              onClick={toggleMobileMenu}
              className={`lg:hidden p-2 rounded-md transition-colors duration-200 ${
                isScrolled
                  ? 'text-primary-red hover:bg-primary-red hover:text-primary-yellow'
                  : 'text-white hover:bg-white hover:text-primary-red'
              }`}
              aria-label="Toggle mobile menu"
            >
              {isMobileMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile Sidebar */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={closeMobileMenu}
              className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            />

            {/* Sidebar */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'tween', duration: 0.3 }}
              className="fixed top-0 right-0 h-full w-80 bg-primary-yellow shadow-xl z-50 lg:hidden"
            >
              <div className="flex flex-col h-full">
                {/* Header */}
                <div className="flex items-center justify-between p-6 border-b border-primary-red/20">
                  <Image
                    src="/images/logo/logo_giallo_topbar.png"
                    alt="Romoletto Logo"
                    width={100}
                    height={33}
                    className="h-8 w-auto"
                  />
                  <button
                    onClick={closeMobileMenu}
                    className="p-2 rounded-md text-primary-red hover:bg-primary-red hover:text-primary-yellow transition-colors duration-200"
                    aria-label="Close mobile menu"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>

                {/* Navigation Links */}
                <div className="flex-1 px-6 py-8">
                  <nav className="space-y-6">
                    <Link
                      href="#storia"
                      onClick={closeMobileMenu}
                      className="block text-lg font-body font-medium text-primary-red hover:text-dark-red transition-colors duration-200"
                    >
                      La Nostra Storia
                    </Link>
                    <Link
                      href="#lavora"
                      onClick={closeMobileMenu}
                      className="block text-lg font-body font-medium text-primary-red hover:text-dark-red transition-colors duration-200"
                    >
                      Lavora con Noi
                    </Link>
                    <Link
                      href="#prenotazione"
                      onClick={closeMobileMenu}
                      className="btn-romoletto inline-block mt-4"
                    >
                      Prenotazione
                    </Link>
                  </nav>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
}
