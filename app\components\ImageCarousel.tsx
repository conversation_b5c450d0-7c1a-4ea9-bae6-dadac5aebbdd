'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';

const carouselImages = [
  {
    src: '/images/carousel/DSC02787.webp',
    alt: 'Pizza tradizionale romana',
    title: 'Pizza Tradizionale',
    description: 'La vera pizza romana, croccante e sottile, preparata con ingredienti freschi e di qualità'
  },
  {
    src: '/images/carousel/DSC02814.webp',
    alt: 'Ingredienti freschi',
    title: 'Ingredienti Selezionati',
    description: 'Solo i migliori ingredienti per garantire sapori autentici e genuini'
  },
  {
    src: '/images/carousel/DSC02842.webp',
    alt: 'Ambiente accogliente',
    title: 'Atmosfera Romana',
    description: 'Un ambiente caldo e accogliente nel cuore di Roma'
  },
  {
    src: '/images/carousel/DSC02861.webp',
    alt: '<PERSON><PERSON>ina tradizionale',
    title: 'Tradizione Culinaria',
    description: 'Ricette tramandate di generazione in generazione'
  },
  {
    src: '/images/carousel/DSC02891.webp',
    alt: 'Pizza al taglio',
    title: 'Pizza al Taglio',
    description: 'La specialità della casa: pizza al taglio come una volta'
  },
  {
    src: '/images/carousel/DSC02915.webp',
    alt: 'Preparazione artigianale',
    title: 'Lavorazione Artigianale',
    description: 'Ogni pizza è preparata a mano con cura e passione'
  },
  {
    src: '/images/carousel/DSC02955.webp',
    alt: 'Sapori autentici',
    title: 'Sapori Autentici',
    description: 'Il gusto vero della tradizione romana in ogni morso'
  }
];

export default function ImageCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === carouselImages.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000); // Change image every 5 seconds

    return () => clearInterval(interval);
  }, []);

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? carouselImages.length - 1 : currentIndex - 1);
  };

  const goToNext = () => {
    setCurrentIndex(currentIndex === carouselImages.length - 1 ? 0 : currentIndex + 1);
  };

  return (
    <section className="py-16 lg:py-24 bg-off-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-heading font-bold text-dark-gray mb-4">
            La Nostra Passione
          </h2>
          <p className="text-lg text-dark-gray/80 font-body max-w-2xl mx-auto">
            Scopri l'autenticità della cucina romana attraverso le nostre specialità, 
            preparate con ingredienti freschi e ricette tradizionali.
          </p>
        </div>

        {/* Carousel Container */}
        <div className="relative">
          <div className="relative h-96 lg:h-[500px] rounded-lg overflow-hidden shadow-2xl">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0"
              >
                <Image
                  src={carouselImages[currentIndex].src}
                  alt={carouselImages[currentIndex].alt}
                  fill
                  className="object-cover"
                  priority={currentIndex === 0}
                />
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
                
                {/* Text Content */}
                <div className="absolute bottom-0 left-0 right-0 p-6 lg:p-8 text-white">
                  <motion.h3
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                    className="text-2xl lg:text-3xl font-heading font-bold mb-2"
                  >
                    {carouselImages[currentIndex].title}
                  </motion.h3>
                  <motion.p
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                    className="text-base lg:text-lg font-body opacity-90 max-w-2xl"
                  >
                    {carouselImages[currentIndex].description}
                  </motion.p>
                </div>
              </motion.div>
            </AnimatePresence>

            {/* Navigation Arrows */}
            <button
              onClick={goToPrevious}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-2 rounded-full transition-all duration-200 backdrop-blur-sm"
              aria-label="Previous image"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            
            <button
              onClick={goToNext}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-2 rounded-full transition-all duration-200 backdrop-blur-sm"
              aria-label="Next image"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-6 space-x-2">
            {carouselImages.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  index === currentIndex
                    ? 'bg-primary-red scale-110'
                    : 'bg-dark-gray/30 hover:bg-dark-gray/50'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
