import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>, Source_Sans_3, Montserrat } from "next/font/google";
import StructuredData from "./components/StructuredData";
import "./globals.css";

const o<PERSON><PERSON> = <PERSON>({
  variable: "--font-heading",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

const sourceSans = Source_Sans_3({
  variable: "--font-body",
  subsets: ["latin"],
  weight: ["300", "400", "600", "700"],
});

const montserrat = Montserrat({
  variable: "--font-button",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "Romoletto - Autentica Pizzeria Romana | Roma",
  description: "Scopri Romoletto, l'autentica pizzeria romana nel cuore di Roma. Pizza al taglio tradizionale, ingredienti freschi e sapori genuini della tradizione culinaria romana.",
  keywords: "pizzeria roma, pizza al taglio, cucina romana, ristorante roma, pizza tradizionale, romoletto",
  authors: [{ name: "Romoletto" }],
  creator: "Romoletto",
  publisher: "Romoletto",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://romoletto.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "Romoletto - Autentica Pizzeria Romana",
    description: "L'autentica pizzeria romana nel cuore di Roma. Pizza al taglio tradizionale e cucina genuina.",
    url: "https://romoletto.com",
    siteName: "Romoletto",
    locale: "it_IT",
    type: "website",
    images: [
      {
        url: "/images/logo/logo_giallo_topbar.png",
        width: 1200,
        height: 630,
        alt: "Romoletto - Pizzeria Romana",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Romoletto - Autentica Pizzeria Romana",
    description: "L'autentica pizzeria romana nel cuore di Roma. Pizza al taglio tradizionale e cucina genuina.",
    images: ["/images/logo/logo_giallo_topbar.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="it">
      <head>
        <link rel="icon" href="/images/logo/favicon.png" />
        <link rel="apple-touch-icon" href="/images/logo/favicon.png" />
        <StructuredData />
      </head>
      <body
        className={`${oswald.variable} ${sourceSans.variable} ${montserrat.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
