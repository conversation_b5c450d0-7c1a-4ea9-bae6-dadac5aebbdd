'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowLeft, Calendar, Clock, Users, Phone, User, Mail } from 'lucide-react';

interface FormData {
  nome: string;
  cognome: string;
  telefono: string;
  email: string;
  data: string;
  orario: string;
  persone: string;
  note: string;
}

export default function PrenotazionePage() {
  const [formData, setFormData] = useState<FormData>({
    nome: '',
    cognome: '',
    telefono: '',
    email: '',
    data: '',
    orario: '',
    persone: '2',
    note: ''
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      const response = await fetch('/api/prenotazione', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setSubmitStatus('success');
        setFormData({
          nome: '',
          cognome: '',
          telefono: '',
          email: '',
          data: '',
          orario: '',
          persone: '2',
          note: ''
        });
      } else {
        setSubmitStatus('error');
      }
    } catch (error) {
      console.error('Errore invio prenotazione:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Generate time options
  const timeOptions = [];
  for (let hour = 12; hour <= 15; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      timeOptions.push(time);
    }
  }
  for (let hour = 19; hour <= 23; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      timeOptions.push(time);
    }
  }

  return (
    <div className="min-h-screen bg-off-white">
      {/* Header */}
      <header className="bg-primary-yellow shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center">
            <Link
              href="/"
              className="flex items-center space-x-2 text-primary-red hover:text-dark-red transition-colors duration-200"
            >
              <ArrowLeft className="w-5 h-5" />
              <span className="font-body font-medium">Torna alla Home</span>
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="text-center mb-8">
            <h1 className="text-3xl lg:text-4xl font-heading font-bold text-dark-gray mb-4">
              Prenota il tuo Tavolo
            </h1>
            <p className="text-lg text-dark-gray/80 font-body">
              Compila il form per prenotare un tavolo da Romoletto. 
              Ti contatteremo per confermare la disponibilità.
            </p>
          </div>

          {/* Success Message */}
          {submitStatus === 'success' && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6"
            >
              <p className="font-body">
                ✅ Prenotazione inviata con successo! Ti contatteremo presto per confermare.
              </p>
            </motion.div>
          )}

          {/* Error Message */}
          {submitStatus === 'error' && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6"
            >
              <p className="font-body">
                ❌ Errore nell'invio della prenotazione. Riprova o contattaci direttamente.
              </p>
            </motion.div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-xl p-6 lg:p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Nome */}
              <div>
                <label htmlFor="nome" className="block text-sm font-body font-semibold text-dark-gray mb-2">
                  <User className="w-4 h-4 inline mr-2" />
                  Nome *
                </label>
                <input
                  type="text"
                  id="nome"
                  name="nome"
                  value={formData.nome}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-red focus:border-transparent font-body"
                  placeholder="Il tuo nome"
                />
              </div>

              {/* Cognome */}
              <div>
                <label htmlFor="cognome" className="block text-sm font-body font-semibold text-dark-gray mb-2">
                  <User className="w-4 h-4 inline mr-2" />
                  Cognome *
                </label>
                <input
                  type="text"
                  id="cognome"
                  name="cognome"
                  value={formData.cognome}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-red focus:border-transparent font-body"
                  placeholder="Il tuo cognome"
                />
              </div>

              {/* Telefono */}
              <div>
                <label htmlFor="telefono" className="block text-sm font-body font-semibold text-dark-gray mb-2">
                  <Phone className="w-4 h-4 inline mr-2" />
                  Telefono *
                </label>
                <input
                  type="tel"
                  id="telefono"
                  name="telefono"
                  value={formData.telefono}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-red focus:border-transparent font-body"
                  placeholder="+39 ************"
                />
              </div>

              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-sm font-body font-semibold text-dark-gray mb-2">
                  <Mail className="w-4 h-4 inline mr-2" />
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-red focus:border-transparent font-body"
                  placeholder="<EMAIL>"
                />
              </div>

              {/* Data */}
              <div>
                <label htmlFor="data" className="block text-sm font-body font-semibold text-dark-gray mb-2">
                  <Calendar className="w-4 h-4 inline mr-2" />
                  Data *
                </label>
                <input
                  type="date"
                  id="data"
                  name="data"
                  value={formData.data}
                  onChange={handleInputChange}
                  required
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-red focus:border-transparent font-body"
                />
              </div>

              {/* Orario */}
              <div>
                <label htmlFor="orario" className="block text-sm font-body font-semibold text-dark-gray mb-2">
                  <Clock className="w-4 h-4 inline mr-2" />
                  Orario *
                </label>
                <select
                  id="orario"
                  name="orario"
                  value={formData.orario}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-red focus:border-transparent font-body"
                >
                  <option value="">Seleziona orario</option>
                  {timeOptions.map(time => (
                    <option key={time} value={time}>{time}</option>
                  ))}
                </select>
              </div>

              {/* Numero persone */}
              <div className="md:col-span-2">
                <label htmlFor="persone" className="block text-sm font-body font-semibold text-dark-gray mb-2">
                  <Users className="w-4 h-4 inline mr-2" />
                  Numero di persone *
                </label>
                <select
                  id="persone"
                  name="persone"
                  value={formData.persone}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-red focus:border-transparent font-body"
                >
                  {[1,2,3,4,5,6,7,8,9,10].map(num => (
                    <option key={num} value={num.toString()}>
                      {num} {num === 1 ? 'persona' : 'persone'}
                    </option>
                  ))}
                </select>
              </div>

              {/* Note */}
              <div className="md:col-span-2">
                <label htmlFor="note" className="block text-sm font-body font-semibold text-dark-gray mb-2">
                  Note aggiuntive
                </label>
                <textarea
                  id="note"
                  name="note"
                  value={formData.note}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-red focus:border-transparent font-body"
                  placeholder="Eventuali richieste speciali o note..."
                />
              </div>
            </div>

            {/* Submit Button */}
            <div className="mt-8">
              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full btn-romoletto py-4 text-lg ${
                  isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isSubmitting ? 'Invio in corso...' : 'Invia Prenotazione'}
              </button>
            </div>

            <div className="mt-4 text-center">
              <p className="text-sm text-dark-gray/60 font-body">
                * Campi obbligatori. Ti contatteremo per confermare la disponibilità.
              </p>
            </div>
          </form>
        </motion.div>
      </main>
    </div>
  );
}
