'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { FileText, Download, ExternalLink } from 'lucide-react';

export default function MenuSection() {
  const [isHovered, setIsHovered] = useState(false);

  const openMenu = () => {
    window.open('/pdf/menu_romoletto.pdf', '_blank');
  };

  const downloadMenu = () => {
    const link = document.createElement('a');
    link.href = '/pdf/menu_romoletto.pdf';
    link.download = 'Menu_Romoletto.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <section id="menu" className="py-16 lg:py-24 bg-primary-yellow">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="mb-12"
          >
            <h2 className="text-3xl lg:text-4xl font-heading font-bold text-primary-red mb-4">
              Il Nostro Menu
            </h2>
            <p className="text-lg text-dark-gray font-body max-w-2xl mx-auto mb-8">
              Scopri tutte le nostre specialità: dalla pizza al taglio tradizionale 
              ai piatti della cucina romana più autentica.
            </p>
          </motion.div>

          {/* Menu Card */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="max-w-md mx-auto"
          >
            <div
              className="bg-white rounded-lg shadow-xl p-8 cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-2xl"
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              onClick={openMenu}
            >
              {/* Menu Icon */}
              <motion.div
                animate={{ rotate: isHovered ? 5 : 0 }}
                transition={{ duration: 0.3 }}
                className="mb-6"
              >
                <div className="w-20 h-20 bg-primary-red rounded-full flex items-center justify-center mx-auto">
                  <FileText className="w-10 h-10 text-primary-yellow" />
                </div>
              </motion.div>

              {/* Menu Title */}
              <h3 className="text-2xl font-heading font-bold text-dark-gray mb-4">
                Menu Completo
              </h3>

              {/* Menu Description */}
              <p className="text-dark-gray/80 font-body mb-6 leading-relaxed">
                Sfoglia il nostro menu completo con tutte le pizze, antipasti, 
                primi piatti e dolci della tradizione romana.
              </p>

              {/* Action Buttons */}
              <div className="space-y-3">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    openMenu();
                  }}
                  className="btn-romoletto w-full flex items-center justify-center space-x-2"
                >
                  <ExternalLink className="w-5 h-5" />
                  <span>Visualizza Menu</span>
                </button>
                
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    downloadMenu();
                  }}
                  className="w-full bg-transparent border-2 border-primary-red text-primary-red hover:bg-primary-red hover:text-white transition-all duration-300 py-3 px-6 rounded-md font-button font-semibold flex items-center justify-center space-x-2"
                >
                  <Download className="w-5 h-5" />
                  <span>Scarica PDF</span>
                </button>
              </div>

              {/* Additional Info */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <p className="text-sm text-dark-gray/60 font-body">
                  Menu aggiornato al {new Date().toLocaleDateString('it-IT', { 
                    month: 'long', 
                    year: 'numeric' 
                  })}
                </p>
              </div>
            </div>
          </motion.div>

          {/* Additional Menu Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto"
          >
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-red rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-primary-yellow font-bold text-xl">🍕</span>
              </div>
              <h4 className="font-heading font-semibold text-dark-gray mb-2">Pizza al Taglio</h4>
              <p className="text-sm text-dark-gray/70 font-body">
                La nostra specialità con impasto tradizionale romano
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-primary-red rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-primary-yellow font-bold text-xl">🍝</span>
              </div>
              <h4 className="font-heading font-semibold text-dark-gray mb-2">Cucina Romana</h4>
              <p className="text-sm text-dark-gray/70 font-body">
                Piatti tradizionali della cucina capitolina
              </p>
            </div>

            <div className="text-center">
              <div className="w-12 h-12 bg-primary-red rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-primary-yellow font-bold text-xl">🥗</span>
              </div>
              <h4 className="font-heading font-semibold text-dark-gray mb-2">Ingredienti Freschi</h4>
              <p className="text-sm text-dark-gray/70 font-body">
                Solo prodotti di qualità e stagionali
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
