'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON>, Settings, Check } from 'lucide-react';

interface CookiePreferences {
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
}

export default function CookieConsent() {
  const [showBanner, setShowBanner] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true, // Always true, cannot be disabled
    analytics: false,
    marketing: false,
  });

  useEffect(() => {
    // Check if user has already made a choice
    const consent = localStorage.getItem('romoletto-cookie-consent');
    if (!consent) {
      // Show banner after a short delay
      const timer = setTimeout(() => {
        setShowBanner(true);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      // Load saved preferences
      const savedPreferences = JSON.parse(consent);
      setPreferences(savedPreferences);
      updateGoogleConsent(savedPreferences);
    }
  }, []);

  const updateGoogleConsent = (prefs: CookiePreferences) => {
    // Update Google Consent Mode v2
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('consent', 'update', {
        analytics_storage: prefs.analytics ? 'granted' : 'denied',
        ad_storage: prefs.marketing ? 'granted' : 'denied',
        ad_user_data: prefs.marketing ? 'granted' : 'denied',
        ad_personalization: prefs.marketing ? 'granted' : 'denied',
      });
    }
  };

  const acceptAll = () => {
    const newPreferences = {
      necessary: true,
      analytics: true,
      marketing: true,
    };
    setPreferences(newPreferences);
    savePreferences(newPreferences);
    setShowBanner(false);
    setShowSettings(false);
  };

  const acceptNecessary = () => {
    const newPreferences = {
      necessary: true,
      analytics: false,
      marketing: false,
    };
    setPreferences(newPreferences);
    savePreferences(newPreferences);
    setShowBanner(false);
    setShowSettings(false);
  };

  const saveCustomPreferences = () => {
    savePreferences(preferences);
    setShowBanner(false);
    setShowSettings(false);
  };

  const savePreferences = (prefs: CookiePreferences) => {
    localStorage.setItem('romoletto-cookie-consent', JSON.stringify(prefs));
    updateGoogleConsent(prefs);
  };

  const togglePreference = (key: keyof CookiePreferences) => {
    if (key === 'necessary') return; // Cannot disable necessary cookies
    setPreferences(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  return (
    <AnimatePresence>
      {showBanner && (
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t-4 border-primary-red shadow-2xl"
        >
          <div className="max-w-7xl mx-auto p-4 lg:p-6">
            {!showSettings ? (
              // Main Banner
              <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                <div className="flex items-start space-x-3 flex-1">
                  <Cookie className="w-6 h-6 text-primary-red flex-shrink-0 mt-1" />
                  <div>
                    <h3 className="font-heading font-semibold text-dark-gray mb-2">
                      Utilizziamo i Cookie
                    </h3>
                    <p className="text-sm text-dark-gray/80 font-body leading-relaxed">
                      Utilizziamo cookie per migliorare la tua esperienza sul nostro sito, 
                      analizzare il traffico e personalizzare i contenuti. Puoi accettare tutti 
                      i cookie o personalizzare le tue preferenze.
                    </p>
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-3 w-full lg:w-auto">
                  <button
                    onClick={() => setShowSettings(true)}
                    className="flex items-center justify-center space-x-2 px-4 py-2 border-2 border-primary-red text-primary-red hover:bg-primary-red hover:text-white transition-all duration-200 rounded-md font-button font-medium"
                  >
                    <Settings className="w-4 h-4" />
                    <span>Personalizza</span>
                  </button>
                  <button
                    onClick={acceptNecessary}
                    className="px-4 py-2 bg-gray-500 text-white hover:bg-gray-600 transition-all duration-200 rounded-md font-button font-medium"
                  >
                    Solo Necessari
                  </button>
                  <button
                    onClick={acceptAll}
                    className="btn-romoletto"
                  >
                    Accetta Tutti
                  </button>
                </div>
              </div>
            ) : (
              // Settings Panel
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h3 className="font-heading font-semibold text-dark-gray text-lg">
                    Preferenze Cookie
                  </h3>
                  <button
                    onClick={() => setShowSettings(false)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
                  >
                    <X className="w-5 h-5 text-dark-gray" />
                  </button>
                </div>

                <div className="space-y-4 mb-6">
                  {/* Necessary Cookies */}
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-body font-semibold text-dark-gray mb-1">
                        Cookie Necessari
                      </h4>
                      <p className="text-sm text-dark-gray/70">
                        Essenziali per il funzionamento del sito web
                      </p>
                    </div>
                    <div className="flex items-center">
                      <Check className="w-5 h-5 text-green-500" />
                      <span className="ml-2 text-sm text-green-600 font-medium">Sempre attivi</span>
                    </div>
                  </div>

                  {/* Analytics Cookies */}
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-body font-semibold text-dark-gray mb-1">
                        Cookie Analitici
                      </h4>
                      <p className="text-sm text-dark-gray/70">
                        Ci aiutano a capire come utilizzi il sito
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={preferences.analytics}
                        onChange={() => togglePreference('analytics')}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-red"></div>
                    </label>
                  </div>

                  {/* Marketing Cookies */}
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-body font-semibold text-dark-gray mb-1">
                        Cookie Marketing
                      </h4>
                      <p className="text-sm text-dark-gray/70">
                        Per mostrarti contenuti personalizzati
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={preferences.marketing}
                        onChange={() => togglePreference('marketing')}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-red"></div>
                    </label>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-3 justify-end">
                  <button
                    onClick={acceptNecessary}
                    className="px-6 py-2 bg-gray-500 text-white hover:bg-gray-600 transition-all duration-200 rounded-md font-button font-medium"
                  >
                    Solo Necessari
                  </button>
                  <button
                    onClick={saveCustomPreferences}
                    className="btn-romoletto"
                  >
                    Salva Preferenze
                  </button>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
