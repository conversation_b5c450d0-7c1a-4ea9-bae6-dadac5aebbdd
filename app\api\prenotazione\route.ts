import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { nome, cognome, telefono, email, data, orario, persone, note } = body;

    // Validazione dei campi obbligatori
    if (!nome || !cognome || !telefono || !data || !orario || !persone) {
      return NextResponse.json(
        { error: 'Campi obbligatori mancanti' },
        { status: 400 }
      );
    }

    // Configurazione del trasportatore email
    const transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });

    // Formattazione della data
    const dataFormatted = new Date(data).toLocaleDateString('it-IT', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Template email
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #F7C629; padding: 20px; text-align: center;">
          <h1 style="color: #E63422; margin: 0;">🍕 Nuova Prenotazione - Romoletto</h1>
        </div>
        
        <div style="padding: 30px; background-color: #ffffff;">
          <h2 style="color: #222222; border-bottom: 2px solid #E63422; padding-bottom: 10px;">
            Dettagli Prenotazione
          </h2>
          
          <div style="margin: 20px 0;">
            <table style="width: 100%; border-collapse: collapse;">
              <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 10px 0; font-weight: bold; color: #E63422;">Nome:</td>
                <td style="padding: 10px 0;">${nome} ${cognome}</td>
              </tr>
              <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 10px 0; font-weight: bold; color: #E63422;">Telefono:</td>
                <td style="padding: 10px 0;">${telefono}</td>
              </tr>
              ${email ? `
              <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 10px 0; font-weight: bold; color: #E63422;">Email:</td>
                <td style="padding: 10px 0;">${email}</td>
              </tr>
              ` : ''}
              <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 10px 0; font-weight: bold; color: #E63422;">Data:</td>
                <td style="padding: 10px 0;">${dataFormatted}</td>
              </tr>
              <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 10px 0; font-weight: bold; color: #E63422;">Orario:</td>
                <td style="padding: 10px 0;">${orario}</td>
              </tr>
              <tr style="border-bottom: 1px solid #eee;">
                <td style="padding: 10px 0; font-weight: bold; color: #E63422;">Persone:</td>
                <td style="padding: 10px 0;">${persone}</td>
              </tr>
              ${note ? `
              <tr>
                <td style="padding: 10px 0; font-weight: bold; color: #E63422; vertical-align: top;">Note:</td>
                <td style="padding: 10px 0;">${note}</td>
              </tr>
              ` : ''}
            </table>
          </div>
          
          <div style="background-color: #F5F5F5; padding: 15px; border-radius: 5px; margin-top: 20px;">
            <p style="margin: 0; color: #666; font-size: 14px;">
              <strong>Ricorda:</strong> Contatta il cliente per confermare la disponibilità e finalizzare la prenotazione.
            </p>
          </div>
        </div>
        
        <div style="background-color: #222222; color: white; padding: 20px; text-align: center;">
          <p style="margin: 0;">Romoletto - Autentica Pizzeria Romana</p>
          <p style="margin: 5px 0 0 0; font-size: 14px; opacity: 0.8;">
            Via Roma, 123 - 00100 Roma | Tel: +39 123 456 789
          </p>
        </div>
      </div>
    `;

    // Invio email
    await transporter.sendMail({
      from: process.env.SMTP_FROM || process.env.SMTP_USER,
      to: process.env.RESERVATION_EMAIL,
      subject: `🍕 Nuova Prenotazione - ${nome} ${cognome} - ${dataFormatted} ${orario}`,
      html: emailHtml,
      text: `
        Nuova Prenotazione Romoletto
        
        Nome: ${nome} ${cognome}
        Telefono: ${telefono}
        ${email ? `Email: ${email}` : ''}
        Data: ${dataFormatted}
        Orario: ${orario}
        Persone: ${persone}
        ${note ? `Note: ${note}` : ''}
      `,
    });

    // Email di conferma al cliente (se ha fornito l'email)
    if (email) {
      const confirmationHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #F7C629; padding: 20px; text-align: center;">
            <h1 style="color: #E63422; margin: 0;">🍕 Grazie per la tua Prenotazione!</h1>
          </div>
          
          <div style="padding: 30px; background-color: #ffffff;">
            <p style="font-size: 16px; color: #222222;">Ciao <strong>${nome}</strong>,</p>
            
            <p style="color: #666; line-height: 1.6;">
              Abbiamo ricevuto la tua richiesta di prenotazione per <strong>${persone} ${persone === '1' ? 'persona' : 'persone'}</strong> 
              il <strong>${dataFormatted}</strong> alle ore <strong>${orario}</strong>.
            </p>
            
            <p style="color: #666; line-height: 1.6;">
              Ti contatteremo presto al numero <strong>${telefono}</strong> per confermare la disponibilità.
            </p>
            
            <div style="background-color: #F5F5F5; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <h3 style="color: #E63422; margin-top: 0;">I tuoi dettagli:</h3>
              <p style="margin: 5px 0;"><strong>Data:</strong> ${dataFormatted}</p>
              <p style="margin: 5px 0;"><strong>Orario:</strong> ${orario}</p>
              <p style="margin: 5px 0;"><strong>Persone:</strong> ${persone}</p>
              ${note ? `<p style="margin: 5px 0;"><strong>Note:</strong> ${note}</p>` : ''}
            </div>
            
            <p style="color: #666; line-height: 1.6;">
              Non vediamo l'ora di accoglierti da Romoletto per farti gustare la vera pizza romana!
            </p>
          </div>
          
          <div style="background-color: #222222; color: white; padding: 20px; text-align: center;">
            <p style="margin: 0; font-size: 18px;">Romoletto</p>
            <p style="margin: 5px 0 0 0; opacity: 0.8;">Autentica Pizzeria Romana</p>
            <p style="margin: 10px 0 0 0; font-size: 14px; opacity: 0.8;">
              Via Roma, 123 - 00100 Roma<br>
              Tel: +39 123 456 789 | Email: <EMAIL>
            </p>
          </div>
        </div>
      `;

      await transporter.sendMail({
        from: process.env.SMTP_FROM || process.env.SMTP_USER,
        to: email,
        subject: '🍕 Conferma Prenotazione - Romoletto',
        html: confirmationHtml,
      });
    }

    return NextResponse.json(
      { message: 'Prenotazione inviata con successo' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Errore invio prenotazione:', error);
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    );
  }
}
